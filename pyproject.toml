[tool.poetry]
name = "analytics"
version = "0.1.0"
description = "Superset Analytics"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.10"
psycopg2-binary = "2.9.9"
authlib = "1.6.0"
pinotdb = "5.7.0"
flask-cors = "6.0.1"
requests = "2.32.4"
isort = "^5.10.1"
python-dotenv = "^1.1.1"
flask = "2.2.5"
celery = "5.4.0"
click = ">=8.0.3"
pytest = "^8.4.1"


[tool.poetry.group.dev.dependencies]
isort = "^5.10.1"
black = "^22.3.0"
flake8 = "^6.1.0"
pre-commit = "^4.2.0"

[tool.black]
line-length = 79

[tool.isort]
line_length = 79

[tool.flake8]
max-line-length = 79

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
