

# Keycloak Auth
OAUTH_USERNAME=<EMAIL>
OAUTH_PASSWORD=Test
SQLALCHEMY_DATABASE_URI="********************************************/superset"
REDIS_HOST=""
REDIS_PORT=""
SUPERSET_SECRET_KEY=""
SUPERSET_BASE_URL=http://localhost:8088
ISSUER_URL = http://localhost:8080/realms/superset
CLIENT_ID = superset-client
CLIENT_SECRET = 2UyzyO5eo4fXbOaXEINxdYFlTIKfDMyQ
JWT_PUBLIC_KEY="JWT_PUBLIC_KEY"

ANALYTICS_APP_ROUTE = "https://app.dev.spogconnected.com"

# SPOG DOMAIN

ENV_DOMAIN=https://dev.spogconnected.com
EXTERNAL_SQLALCHEMY_URI=pinot://pinot-broker:8099/query/sql?server=http://pinot-controller:9000/
