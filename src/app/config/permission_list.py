full_permissions = [
    ("CssTemplate", "can_read"),
    ("CssTemplate", "can_write"),
    ("ReportSchedule", "can_read"),
    ("ReportSchedule", "can_write"),
    ("Chart", "can_read"),
    ("Chart", "can_write"),
    ("Annotation", "can_read"),
    ("Annotation", "can_write"),
    ("Dashboard", "can_read"),
    ("Dashboard", "can_write"),
    ("Database", "can_read"),
    ("Database", "can_csv_upload"),
    ("Database", "can_excel_upload"),
    ("Dataset", "can_read"),
    ("Dataset", "can_export"),
    ("Dataset", "can_get_or_create_dataset"),
    ("ResetMyPasswordView", "can_this_form_get"),
    ("ResetMyPasswordView", "can_this_form_post"),
    ("OpenApi", "can_get"),
    ("SwaggerView", "can_show"),
    ("MenuApi", "can_get"),
    ("AsyncEventsRestApi", "can_list"),
    ("AdvancedDataType", "can_read"),
    ("AvailableDomains", "can_read"),
    ("CacheRestApi", "can_invalidate"),
    ("Chart", "can_export"),
    ("DashboardFilterStateRestApi", "can_write"),
    ("DashboardFilterStateRestApi", "can_read"),
    ("DashboardPermalinkRestApi", "can_write"),
    ("DashboardPermalinkRestApi", "can_read"),
    ("Dashboard", "can_export"),
    ("Dashboard", "can_get_embedded"),
    ("Dashboard", "can_delete_embedded"),
    ("Dashboard", "can_cache_dashboard_screenshot"),
    ("Datasource", "can_get_column_values"),
    ("EmbeddedDashboard", "can_read"),
    ("Explore", "can_read"),
    ("ExploreFormDataRestApi", "can_write"),
    ("ExploreFormDataRestApi", "can_read"),
    ("ExplorePermalinkRestApi", "can_write"),
    ("ExplorePermalinkRestApi", "can_read"),
    ("ImportExportRestApi", "can_export"),
    ("Tag", "can_write"),
    ("Tag", "can_bulk_create"),
    ("Tag", "can_read"),
    ("DynamicPlugin", "can_show"),
    ("DynamicPlugin", "can_list"),
    ("Api", "can_query"),
    ("Api", "can_query_form_data"),
    ("Datasource", "can_get"),
    ("Datasource", "can_save"),
    ("Datasource", "can_external_metadata"),
    ("Datasource", "can_samples"),
    ("KV", "can_store"),
    ("KV", "can_get_value"),
    ("Superset", "can_sqllab_history"),
    ("Superset", "can_fetch_datasource_metadata"),
    ("Superset", "can_dashboard_permalink"),
    ("Superset", "can_explore_json"),
    ("Superset", "can_log"),
    ("Superset", "can_slice"),
    ("Superset", "can_dashboard"),
    ("Superset", "can_explore"),
    ("TableSchemaView", "can_expanded"),
    ("TableSchemaView", "can_post"),
    ("TableSchemaView", "can_delete"),
    ("TabStateView", "can_post"),
    ("TabStateView", "can_delete_query"),
    ("Tags", "can_list"),
    ("TagView", "can_tags"),
    ("Log", "can_recent_activity"),
    ("SecurityRestApi", "can_read"),
    ("RowLevelSecurity", "can_read"),
    ("Home", "menu_access"),
    ("Superset", "can_share_dashboard"),
    ("Superset", "can_share_chart"),
    ("Chart", "can_tag"),
    ("Dashboard", "can_tag"),
    ("UserOAuthModelView", "can_userinfo"),
    ("PermissionModelView", "can_list"),
    ("ViewMenuModelView", "can_list"),
    ("PermissionViewModelView", "can_list"),
    ("all_database_access", "all_database_access"),
]

menu_access = [
    "Dashboards",
    "Charts",
    "Databases",
    "Manage",
    "Plugins",
    "Css Templates",
    "Tags",
    "Alerts & Reports",
    "Base Permissions",
    "Views/Menus",
    "Permission on Views/Menus",
]
