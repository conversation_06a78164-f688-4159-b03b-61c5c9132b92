import os
from typing import Literal


class Config:
    ROLE_NAME = "DashboardOwner"
    RLS_ENABLED = True
    RLS_FILTER_TYPE = "Regular"
    RLS_FILTER = (
        "{{ '1=1' if account_id() == -1 else "
        "'AccountID=%s' % account_id() }}"
    )
    RLS_DESCRIPTION = "Account id row level security"
    RLS_GROUP_KEY = None

    def __init__(self, **kwargs):
        # Always read ENV from environment, then kwargs
        self.ENV: Literal[
            "spogdev", "spogtest", "spogstage", "spogprodkafka"
        ] = os.environ.get("ENV") or kwargs.get("ENV")

        # Allow ENV override from kwargs if provided
        if "ENV" in kwargs:
            self.ENV = os.environ.get("ENV")

        prefix_map = {
            "spogdev": "dev",
            "spogtest": "test",
            "spogstage": "stage",
            "spogprodkafka": "",  # no prefix for prod
        }

        prefix = prefix_map.get(self.ENV)
        if prefix is None:
            raise ValueError(
                f"Invalid ENV: '{self.ENV}'. "
                f"Must be one of {list(prefix_map.keys())}"
            )

        def fmt(name: str) -> str:
            return f"{prefix}_{name}" if prefix else name

        # Dynamically assign table and schema names
        self.CDR_DATA_TABLE = fmt("cdr_data")
        self.CDR_SMS_TABLE = fmt("cdr_sms")
        self.CDR_VOICE_TABLE = fmt("cdr_voice")
        self.CDR_OPERATOR_TABLE = fmt("cdr_operator")

        # Set dynamic RLS_TABLES based on ENV
        tables = [
            self.CDR_DATA_TABLE,
            self.CDR_VOICE_TABLE,
            self.CDR_SMS_TABLE,
            self.CDR_OPERATOR_TABLE,
        ]

        self.RLS_TABLES = {
            "DashboardOwner": tables,
            "default": tables,
        }

    # use case
    # RLS_FILTER = {
    #     "DashboardOwner": ["studentID = '{{ studentID() }}'"],
    #     "DashboardOwner": ["TEST", "TRF"],
    #     "test": ["TEST"]
    # }
