import logging
import os

import requests
from config.rls_config import Config
from utils.keycloak import get_keycloak_token

logging.basicConfig(level=logging.INFO)


DB_NAME = "PINOT DATABASE"


class SupersetDatabaseManager:
    def __init__(self, db_name, tables, token):
        if not db_name:
            raise ValueError("Database name must not be empty.")
        if not tables or not isinstance(tables, list):
            raise ValueError("Tables must be a non-empty list.")
        if not token:
            raise ValueError("Access token must not be empty.")
        self.db_name = db_name
        self.tables = tables
        self.token = token
        self.db_id = None
        self.session = requests.Session()

    def get_existing_database_id(self):
        url = f"{os.environ.get('SUPERSET_BASE_URL')}/api/v1/database/"
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        try:
            response = self.session.get(url, headers=headers)
            response.raise_for_status()
            databases = response.json().get("result", [])
            for db in databases:
                if db.get("database_name") == self.db_name:
                    logging.info(
                        f"Database '{self.db_name}' already exists."
                        f"Using existing ID: {db.get('id')}"
                    )
                    return db.get("id")
        except Exception as e:
            logging.error(
                f"Failed to check existing database '{self.db_name}': " f"{e}"
            )
        return None

    def create_database(self):
        url = f"{os.environ.get('SUPERSET_BASE_URL')}/api/v1/database/"
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        self.db_id = self.get_existing_database_id()
        if self.db_id:
            return self.db_id
        payload = {
            "database_name": self.db_name,
            "sqlalchemy_uri": os.environ.get("EXTERNAL_SQLALCHEMY_URI"),
            "expose_in_sqllab": True,
            "allow_ctas": True,
            "allow_cvas": True,
            "allow_run_async": True,
        }
        try:
            response = self.session.post(url, headers=headers, json=payload)
            response.raise_for_status()
            self.db_id = response.json().get("id")
            logging.info(
                f"Database '{self.db_name}' created with ID: {self.db_id}"
            )
            return True
        except Exception as e:
            logging.error(f"Failed to create database '{self.db_name}': {e}")
            return False

    def create_dataset(self, schema, table_name):
        url = f"{os.environ.get('SUPERSET_BASE_URL')}/api/v1/dataset/"
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
        }
        payload = {
            "database": self.db_id,
            "schema": schema,
            "table_name": table_name,
            "owners": [],
        }
        try:
            response = self.session.post(url, headers=headers, json=payload)
            response.raise_for_status()
            logging.info(
                f"Dataset for table '{schema}.{table_name}'"
                f"created: {response.json()}"
            )
            return True
        except Exception as e:
            logging.error(
                f"Failed to create dataset for table"
                f"'{schema}.{table_name}': {e}"
            )
            return False

    def create_multiple_datasets(self):
        for table in self.tables:
            schema = table.get("schema", "public")
            table_name = table.get("table_name")
            success = self.create_dataset(schema, table_name)
            if not success:
                logging.error(
                    f"Aborting further dataset creation due "
                    f"to error on table '{schema}.{table_name}'."
                )
                break


if __name__ == "__main__":
    token = get_keycloak_token()
    config = Config()
    TABLES = [
        {"schema": "default", "table_name": config.CDR_DATA_TABLE},
        {"schema": "default", "table_name": config.CDR_VOICE_TABLE},
        {"schema": "default", "table_name": config.CDR_SMS_TABLE},
        {"schema": "default", "table_name": config.CDR_OPERATOR_TABLE},
    ]
    try:
        manager = SupersetDatabaseManager(
            db_name=DB_NAME, tables=TABLES, token=token
        )
        db_created = manager.create_database()
        if db_created:
            manager.create_multiple_datasets()
        else:
            logging.error(
                "Database creation failed. No datasets will be created."
            )
    except Exception as e:
        logging.error(f"Script failed: {e}")
