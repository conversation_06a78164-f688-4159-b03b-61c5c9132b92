import logging
import urllib.parse
from typing import Dict, List, Optional

import requests


class SupersetClient:
    def __init__(self, base_url, token, TIMEOUT):
        self.base_url = base_url
        self.TIMEOUT = TIMEOUT
        self.session = requests.Session()
        self.headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

    def create_role(self, role_name):
        resp = self.session.post(
            f"{self.base_url}/api/v1/security/roles",
            headers=self.headers,
            json={"name": role_name},
        )
        logging.info(f"create_role:{resp}")
        if resp.status_code == 422:
            logging.info(f"Role '{role_name}' already exists")
        elif resp.ok:
            logging.info(f"Role '{role_name}' created")
        else:
            logging.error(f"Role creation failed: {resp.text}")

    def get_role_id_by_name(self, role_name):
        rn = role_name
        query = urllib.parse.quote_plus(
            f'{{"filters":[{{"col":"name","opr":"eq","value":"{rn}"}}]}}'
        )
        url = f"{self.base_url}/api/v1/security/roles/?q={query}"
        logging.info(f"url:{url}")
        resp = self.session.get(
            url,
            headers=self.headers,
        )
        logging.info(f"get_role_id_by_name:{resp.json()}")
        resp.raise_for_status()
        result = resp.json()["result"]
        if not result:
            raise ValueError("Role not found")
        return result[0]["id"]

    def get_table_ids_by_names(self, table_names: List[str]) -> List[int]:
        url = (
            f'{self.base_url}/api/v1/dataset/?q={{"page":0,"page_size":1000}}'
        )
        resp = self.session.get(url, headers=self.headers)
        logging.info(f"get_table_ids_by_names:{resp.json()}")
        resp.raise_for_status()
        datasets = resp.json().get("result", [])
        return [
            d["id"] for d in datasets if d.get("table_name") in table_names
        ]

    def add_rls_policy(
        self,
        role_id: int,
        filter_type: str,
        filter_clause: str,
        tables: List[int],
        description: str,
        group_key: str,
    ):
        """
        Adds or updates a Row-Level Security (RLS) rule
        for a given role and tables.
        """
        rule_name = f"rls_multi_{role_id}"
        try:
            existing_rule = self._get_rls_rule_by_name(rule_name)
            logging.info(f"existing_rule : {existing_rule}")
            if existing_rule:
                rule_id = existing_rule["id"]
                existing_tables = [
                    next(
                        (
                            table.get("id")
                            for table in existing_rule.get("tables", [])
                            if "id" in table
                        ),
                        None,
                    )
                ]
                logging.info(f"existing_tables : {existing_tables}")
                updated_tables = sorted(set(existing_tables + tables))

                payload = self._build_payload(
                    role_id,
                    filter_type,
                    filter_clause,
                    updated_tables,
                    description,
                    group_key,
                    rule_name,
                )
                self._update_rls_rule(rule_id, payload)
            else:
                payload = self._build_payload(
                    role_id,
                    filter_type,
                    filter_clause,
                    tables,
                    description,
                    group_key,
                    rule_name,
                )
                self._create_rls_rule(payload)

        except requests.RequestException as e:
            logging.error(f"[RLS] Request failed: {e}")
        except Exception as e:
            logging.exception(f"[RLS] Unexpected error: {e}")

    def _get_rls_rule_by_name(self, rule_name: str) -> Optional[Dict]:
        """Retrieve an existing RLS rule by its name."""
        query = urllib.parse.quote_plus(
            (
                '{"filters":['
                f'{{"col":"name","opr":"eq","value":"{rule_name}"}}'
                "]}"
            )
        )
        url = f"{self.base_url}/api/v1/rowlevelsecurity/?q={query}"
        resp = self.session.get(
            url, headers=self.headers, timeout=self.TIMEOUT
        )
        resp.raise_for_status()
        result = resp.json().get("result", [])
        return result[0] if result else None

    def _create_rls_rule(self, payload: Dict):
        """Create a new RLS rule."""
        resp = self.session.post(
            f"{self.base_url}/api/v1/rowlevelsecurity/",
            headers=self.headers,
            json=payload,
            timeout=self.TIMEOUT,
        )
        if resp.status_code == 422:
            logging.info(
                f"RLS already exists for role ID {payload['role_id']}"
            )
        elif resp.ok:
            logging.info(f"[RLS] Created rule: {payload['name']}")
        else:
            logging.error(
                f"[RLS] Failed to create rule:"
                f"{resp.status_code} - {resp.text}"
            )

    def _update_rls_rule(self, rule_id: int, payload: Dict):
        """Update an existing RLS rule."""
        url = f"{self.base_url}/api/v1/rowlevelsecurity/{rule_id}"
        resp = self.session.put(
            url, headers=self.headers, json=payload, timeout=self.TIMEOUT
        )
        if resp.ok:
            logging.info(f"[RLS] Updated rule: {payload['name']}")
        else:
            logging.error(
                f"[RLS] Failed to update rule:"
                f"{resp.status_code} - {resp.text}"
            )

    def _build_payload(
        self,
        role_id: int,
        filter_type: str,
        filter_clause: str,
        tables: List[int],
        description: str,
        group_key: str,
        name: str,
    ) -> Dict:
        """Builds the RLS rule payload."""
        return {
            "clause": filter_clause,
            "description": description,
            "filter_type": filter_type,
            "group_key": group_key,
            "roles": [role_id],
            "tables": tables,
            "name": name,
        }

    def get_permission_view_id(self, full_permissions, menu_access):
        try:
            # Fetch all permission-view entries across pages
            all_permissions = []
            page = 0
            page_size = 100
            while True:
                url = (
                    f"{self.base_url}/api/v1/security/permissions-resources/?"
                    f'q={{"page":{page},"page_size":{page_size}}}'
                )
                response = self.session.get(url, headers=self.headers)
                response.raise_for_status()
                result = response.json().get("result", [])
                if not result:
                    break
                all_permissions.extend(result)
                page += 1
            # Create a map of (view_menu, permission) -> ID
            permission_map = {
                (perm["view_menu"]["name"], perm["permission"]["name"]): perm[
                    "id"
                ]
                for perm in all_permissions
            }

            found_ids = []

            # Match full permissions
            for perm_pair in full_permissions:
                perm_id = permission_map.get(perm_pair)
                if perm_id:
                    found_ids.append(perm_id)
                else:
                    logging.warning(
                        f"Missing permission-view pair: {perm_pair}"
                    )

            # Match menu access ("menu_access" on menus)
            for menu in menu_access:
                perm_id = permission_map.get((menu, "menu_access"))
                if perm_id:
                    found_ids.append(perm_id)
                else:
                    logging.warning(
                        f"Missing menu access for: ({menu}, 'can_read')"
                    )

            return found_ids

        except Exception as e:
            logging.error(f"Failed to fetch filtered permission IDs: {e}")
            return []

    def assign_permissions_to_role(
        self, role_id, role_name, full_permissions, menu_access
    ):
        try:
            permission_view_ids = self.get_permission_view_id(
                full_permissions, menu_access
            )
            url = (
                f"{self.base_url}/api/v1/security/roles/{role_id}/permissions"
            )
            payload = {"permission_view_menu_ids": permission_view_ids}

            response = requests.post(
                url, headers=self.headers, json=payload, timeout=self.TIMEOUT
            )
            response.raise_for_status()

            logging.info(
                f"Permissions successfully updated for role '{role_name}'"
            )

        except requests.exceptions.RequestException as req_err:
            logging.error(
                f"HTTP error while updating permissions for role"
                f"{role_name}: {req_err}"
            )
        except Exception as e:
            logging.error(
                f"Unexpected error during permission assignment for role:"
                f"{role_name}: {e}"
            )
