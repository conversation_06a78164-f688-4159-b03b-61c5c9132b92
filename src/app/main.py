# Entry point for the application
import argparse
import logging
import os

from config.permission_list import full_permissions, menu_access
from config.rls_config import Config
from services.client import SupersetClient
from utils.keycloak import get_keycloak_token

logging.basicConfig(level=logging.INFO)


def main():
    try:
        token = get_keycloak_token()
        logging.info("Token fetched")
        TIMEOUT = 10
        client = SupersetClient(os.getenv("SUPERSET_BASE_URL"), token, TIMEOUT)

        parser = argparse.ArgumentParser(description="Superset utility script")
        parser.add_argument(
            "command",
            choices=["create_rls", "create_permission"],
            help="Choose which script to run",
        )
        args = parser.parse_args()
        config = Config()

        if args.command == "create_permission":
            role_name = config.ROLE_NAME
            client.create_role(role_name)
            role_id = client.get_role_id_by_name(role_name)
            client.assign_permissions_to_role(
                role_id, role_name, full_permissions, menu_access
            )
        elif args.command == "create_rls":
            role_name = config.ROLE_NAME
            client.create_role(role_name)
            role_id = client.get_role_id_by_name(role_name)

            if config.RLS_ENABLED:
                table_names = config.RLS_TABLES.get(
                    role_name, config.RLS_TABLES["default"]
                )
                table_ids = client.get_table_ids_by_names(table_names)
                client.add_rls_policy(
                    role_id,
                    config.RLS_FILTER_TYPE,
                    config.RLS_FILTER,
                    table_ids,
                    config.RLS_DESCRIPTION,
                    config.RLS_GROUP_KEY,
                )
    except Exception as e:
        logging.critical(f"Unhandled error: {e}")


if __name__ == "__main__":
    main()
