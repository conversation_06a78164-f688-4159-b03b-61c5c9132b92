import logging
import os

import requests
from dotenv import load_dotenv

load_dotenv()
logging.basicConfig(level=logging.INFO)

ISSUER_URL = os.getenv("ISSUER_URL")


def get_keycloak_token():
    try:
        token_url = f"{ISSUER_URL}/protocol/openid-connect/token"
        data = {
            "grant_type": "password",
            "client_id": os.getenv("CLIENT_ID"),
            "client_secret": os.getenv("CLIENT_SECRET"),
            "username": os.getenv("OAUTH_USERNAME"),
            "password": os.getenv("OAUTH_PASSWORD"),
        }
        response = requests.post(token_url, data=data, timeout=30)
        response.raise_for_status()
        return response.json()["access_token"]
    except Exception as e:
        logging.error(f"Failed to fetch Keycloak token: {e}")
        raise
