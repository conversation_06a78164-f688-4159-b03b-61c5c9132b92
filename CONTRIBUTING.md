
Superset Management Scripts
In this Superset-based project, custom scripts are integrated into the main.py entry point to manage row-level security and permissions.

🛡️ Create Row-Level Security (RLS) Policies
To define or update RLS policies within Superset, run:

python3 main.py create_rls
This command creates RLS rules that restrict data visibility based on user roles or attributes. These policies integrate with Superset’s built-in RLS features.

🔐 Create Permissions
To generate or sync custom permissions for dashboards, charts, or data sources:


python3 main.py create_permission
This ensures that all necessary permission sets are properly created and associated with Superset roles.


# TO RUN database connection script
# Use this script when you want to create or initialize a database connection and dataset
# Recommended: Run as a module from app
python3 -m services.database_connection
