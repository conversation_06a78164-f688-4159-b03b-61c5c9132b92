import unittest
from unittest.mock import MagicMock, patch

from src.app.services.database_connection import SupersetDatabaseManager


class TestSupersetDatabaseManager(unittest.TestCase):
    def setUp(self):
        self.db_name = "TEST_DB"
        self.tables = [{"schema": "public", "table_name": "TEST_TABLE"}]
        self.token = "fake-token"
        self.manager = SupersetDatabaseManager(
            self.db_name, self.tables, self.token
        )

    @patch("src.app.services.database_connection.requests.Session")
    def test_get_existing_database_id_found(self, mock_session):
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "result": [{"database_name": self.db_name, "id": 123}]
        }
        mock_response.raise_for_status.return_value = None
        mock_session.return_value.get.return_value = mock_response
        self.manager.session = mock_session.return_value
        db_id = self.manager.get_existing_database_id()
        self.assertEqual(db_id, 123)

    @patch("src.app.services.database_connection.requests.Session")
    def test_get_existing_database_id_not_found(self, mock_session):
        mock_response = MagicMock()
        mock_response.json.return_value = {"result": []}
        mock_response.raise_for_status.return_value = None
        mock_session.return_value.get.return_value = mock_response
        self.manager.session = mock_session.return_value
        db_id = self.manager.get_existing_database_id()
        self.assertIsNone(db_id)

    @patch("src.app.services.database_connection.requests.Session")
    def test_create_database_success(self, mock_session):
        self.manager.get_existing_database_id = MagicMock(return_value=None)
        mock_response = MagicMock()
        mock_response.json.return_value = {"id": 456}
        mock_response.raise_for_status.return_value = None
        mock_session.return_value.post.return_value = mock_response
        self.manager.session = mock_session.return_value
        result = self.manager.create_database()
        self.assertTrue(result)
        self.assertEqual(self.manager.db_id, 456)

    @patch("src.app.services.database_connection.requests.Session")
    def test_create_database_failure(self, mock_session):
        self.manager.get_existing_database_id = MagicMock(return_value=None)
        mock_session.return_value.post.side_effect = Exception("fail")
        self.manager.session = mock_session.return_value
        result = self.manager.create_database()
        self.assertFalse(result)

    @patch("src.app.services.database_connection.requests.Session")
    def test_create_dataset_success(self, mock_session):
        self.manager.db_id = 789
        mock_response = MagicMock()
        mock_response.json.return_value = {"id": 1}
        mock_response.raise_for_status.return_value = None
        mock_session.return_value.post.return_value = mock_response
        self.manager.session = mock_session.return_value
        result = self.manager.create_dataset("public", "TEST_TABLE")
        self.assertTrue(result)

    @patch("src.app.services.database_connection.requests.Session")
    def test_create_dataset_failure(self, mock_session):
        self.manager.db_id = 789
        mock_session.return_value.post.side_effect = Exception("fail")
        self.manager.session = mock_session.return_value
        result = self.manager.create_dataset("public", "TEST_TABLE")
        self.assertFalse(result)
