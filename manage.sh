#!/bin/sh -e

export PYTHONPATH=/app:/app/pythonpath

# Function to prepare the Superset configuration file
setup_superset_config() {
    local source_config="/app/superset_config.py"
    local dest_config_path="${SUPERSET_CONFIG_PATH:?SUPERSET_CONFIG_PATH is not set}"

    if [ ! -f "${source_config}" ]; then
        echo "ERROR: Source config not found at ${source_config}" >&2
        exit 1
    fi

    echo "--> Preparing Superset configuration..."
    # Copy the file to the location expected by Superset, assuming the directory exists.
    cp "${source_config}" "${dest_config_path}"
    echo "--> Configuration ready."
}

# Function to run the main service
start_service() {
    echo "--> Superset service starting..."
    setup_superset_config
    # This will execute the command passed to start_service, e.g., "superset webserver"
    exec "$@"
}

run_cmd() {
    /bin/sh -ec "${@}"
}

if [ "$#" -eq 0 ]; then
    echo "ERROR: ${0} requires a management command" >&2
    exit 1
fi

cmd="${1}"
shift

case "${cmd}" in
    start_service|run_cmd)
        "${cmd}" "${@}"
        ;;
    *)
        echo "ERROR: unknown management command '${cmd}'" >&2
        exit 1
        ;;
esac
