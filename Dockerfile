FROM apache/superset:4.1.2
USER root
RUN pip install poetry
COPY pyproject.toml poetry.lock* .
COPY manage.sh .
RUN chmod +x manage.sh
RUN poetry config virtualenvs.create false
RUN poetry install --no-root --no-interaction
COPY src .
COPY --chown=superset src/app/config/superset_config.py /app/
ENV SUPERSET_CONFIG_PATH /app/superset_config.py
RUN rm pyproject.toml poetry.lock*
USER superset
ENTRYPOINT ["./manage.sh"]
CMD ["start_service"]
