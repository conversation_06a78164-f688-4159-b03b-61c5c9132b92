.DEFAULT_GOAL := help
.PHONY: changes coverage install-main-deps install-test-deps install-all-deps test checkout-master start-app

coverage:  ## Run tests with coverage
		coverage erase
		PYTHONPATH=src coverage run --include=src/app/* -m pytest
		coverage report -m

test:
		PYTHONPATH=src pytest -svv

checkout-master:
		git fetch && git checkout master && git pull origin master

changes: checkout-master
		@echo Changes from `git describe --abbrev=0 --tags` tag:
		@git log --no-merges --format="- %s%b" `git describe --abbrev=0 --tags`...origin/master | tee -p changes.txt

start-app:
		PYTHONPATH=src python -m app.main

lint:
		flake8 src tests
		mypy

install-main-deps:
	poetry install --no-root --only main

install-test-deps:
	poetry install --no-root --only test

install-all-deps:
	poetry install --no-root --with test,dev
