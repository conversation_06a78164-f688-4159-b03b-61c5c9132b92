stages:
  - build
  - lint
  - trivy
  - test  
  - secscan
  - push
  - release
  - deploy

variables:

  # set to 'true' if you want continous delivery to DEV
  AUTO_DEPLOY_MASTER: 'false'
  CLUSTER_ENVIRONMENT:
    value: spogdev
    description: Please enter spogdev or spogtest
  # set how long to wait for deployment operations to complete
  # DEPLOYMENT_TIMEOUT: 180s
  # set to 'true' if you want back deploy to OPS button
  # DEPLOY_OPS: 'false'
  # exclude lint job from pipeline when set to 'true'
  # TEST_SKIP_LINT: 'true'
  # exclude mypy job from pipeline when set to 'true'
  TEST_SKIP_MYPY: 'true'
  # exclude pytest job from pipeline when set to 'true'
  TEST_SKIP_PYTEST: 'true'
  # set to 'true' if tests require database
  TEST_REQUIRES_POSTGRES: 'true'
  # set to 'true' if tests require Keycloak (implies previous variable)
  TEST_REQUIRES_KEYCLOAK: 'false'
  # use below variables to overwrite command of the corresponding jobs with your own
  # also variable ${COMMAND} is available and will be evaluated to default command from shared pipeline
  # TEST_COMMAND_LINT:
  # TEST_COMMAND_MYPY:
  # TEST_COMMAND_PYTEST:
  TEST_COMMAND_REQUIREMENTS: poetry install --no-root --no-interaction

include:
  - project: devops/build
    file: /pipelines/build-docker-image-api.yaml
  - project: devops/test
    file: /pipelines/sec-scan.yaml
  - project: devops/test
    file: /pipelines/trivy-scan.yaml
  - project: devops/test
    file: /pipelines/superset-tests.yaml
  - project: devops/tools
    file: /pipelines/tag-release-docker-image.yaml
  - project: devops/tools
    file: /pipelines/create-gitlab-release.yaml
  - project: devops/deploy
    file: /pipelines/trigger-deployment.yaml



lint:
  stage: lint
  extends: .common-python-test 
  needs: ["requirements"]     
  variables:
    COMMAND: flake8         
  script:
    - docker run ${CONTAINER_OPTIONS}  ${TEST_IMAGE} ${RUN_CMD} "${COMMAND}"
  rules:
    - !reference [.python-test-rules, rules]

isort:
  stage: lint
  extends: .common-python-test
  needs: ["requirements"]
  variables:
    # The command to run remains the same
    COMMAND: isort --check --diff .
    # Override the RUN_CMD to bypass the script and call the shell directly.
    # This is functionally equivalent to what manage.sh does.
    RUN_CMD: /bin/sh -ec
  script:
    # This now expands to: docker run ... /bin/sh -ec "isort --check --diff ."
    - docker run ${CONTAINER_OPTIONS} ${TEST_IMAGE} ${RUN_CMD} "${COMMAND}"
  rules:
    - !reference [.python-test-rules, rules]

black:
  stage: lint
  extends: .common-python-test
  needs: ["requirements", "isort"]
  variables:
    # The command to run
    COMMAND: black --check --diff .
    # Override RUN_CMD to bypass manage.sh and call the shell directly
    RUN_CMD: /bin/sh -ec
  script:
    # The --network flag has been removed
    - docker run ${CONTAINER_OPTIONS}  ${TEST_IMAGE} ${RUN_CMD} "${COMMAND}"
  rules:
    - !reference [.python-test-rules, rules]
